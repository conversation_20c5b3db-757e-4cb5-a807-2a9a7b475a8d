短信 http 接口说明文档

V2 版本

文件编号：    BW_SMS_JSON

版本号：      V2.4.2

受控状态：       ■ 受控 □ 非受控

保密级别：       部门级

编制人/编制时间：   魏军          2018-07-05


编制人/编制时间：

仉晓甜

2018-07-09

编制人/编制时间：   魏军          2019-03-21

审核人/审核时间：
批准人/批准时间：
生效日期：

页 1


目录

短信http 接口说明文档                                    1

1.  概述                                            3

2.  下行（不同内容）                                    3

2.1.  地址                                          3

2.2.  入参内容                                       3

2.3.  🎧参内容                                       4

3.  下行（相同内容）                                    6

3.1.  地址                                          6

3.2.  入参内容                                       6

3.3.  🎧参内容                                       7

4.  状态报告                                         8

4.1.  客户获取                                       8

4.1.1.  地址                                       8

4.1.2.  入参内容                                    9

4.1.3.  🎧参内容                                    9

4.2.  平台推送                                      11

4.2.1.   概述                                     11

4.2.2.   状态报告格式                               11

4.2.3.   入参示例                                  13

4.2.4.   响应                                     14

5.  上行                                           14

5.1.  客户获取                                      14

5.1.1.  地址                                      14

5.1.2.  入参内容                                   14

5.1.3.  🎧参内容                                   15

5.2.  平台推送                                      16

5.2.1.   概述                                     16

5.2.2.   上行信息格式                               17

5.2.3.   入参示例                                  18

5.2.4.   响应                                     18

6.  余额查询                                         19

6.1.  地址                                         19

6.1.1.  入参内容                                   19

6.1.2.  🎧参内容                                   19

7.  错误码说明                                       20

页 2


1. 概述

本协议暂定为使用 HTTP 方式提交（post/get）,以 json 格式返回结果数据，针对密码
安全情况，部分交互协议采用了加密的处理。

本接口协议的字符集编码格式（参数和返回结果）统一使用 UTF-8 格式编码，Content-type
统一为application/json。

2. 下行（不同内容）

2.1.  地址

http 地址： http://域名:8080/sms/v2/send-different
https 地址：https://域名:8443/sms/v2/send-different
(   使用域名由客服经理提供)

2.2. 入参内容


一

二级

级

{}

List[]

字段名
账户

事务ID

密码

短信列
表

手机号

内容
自定义
id

扩展码

接口字段

account
transactionId
password

list
mobile

content
uuid

ext

类型

String
String
String

list
String

String
String

String

长度

50

36

200

14

1000

32

16

取值描述

由服务方提供的账
户

事务 ID

计算后的授权密码

下行短信列表
下行手机号
短信内容

短信唯一标识，调用
方自己定义，用于对
应状态报告值。

本条短信扩展码

备注

*必填：账号

*必填：幂等处理使用，要
求每次提交的值不相同

*必填：
account+pwd+transactionI
d          按照顺序排列（不包含
“+”字符）后，再经过标
准MD5 小写加密(其中
pwd 为客户密码，服务方
提供)

上限 200 单次

*必填：只允许单个手机号
码

*必填：

若不填写，默认使用 UUID

生产一个唯一值

非填写没有扩展码时为

页 3


扩展参
数

extraParam

String

1000

本条短信的扩展参
数

null，合法扩展为数字，否
则都按照无扩展处理

非填写

入参示例

{

"account": "ceshizhanghu",

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"password":"5228537688dcbf85c87e59c1ae6b11a3",
"list": [

{

"mobile":"***********",

"content":"短信下发内容 1",
"uuid":"1442301",

"ext":"105"

},

{

"mobile":"***********",

"content":"短信下发内容 2",
"uuid":"1442302",

"ext":"01"

},

{

"mobile":"***********",

"content":"短信下发内容 3",
"uuid":"1442303",

"ext":null

}

]

}

2.3. 出参内容


一

二级  字段名

级

成功标志

{}

事务ID

失败信息

接口字段

success

transactionId

failList

类型

boolean

String
list

长度   取值描述

返回 true 表示都
下行成功，返回
false 表示部分或
全部失败

接口提交时的事
务id 的值

返回失败部分号

备注

全部成功时为空

页 4


List[]

列表

失败号码
错误码

错误信息
自定义 id

mobile
errorCode

errorDesc
uuid

String
String

String
String

码的信息

手机号

10 错误码，由服务
方定义

50 错误信息，由服
务方定义

32  调用方自己定义，
用于对应状态报
告值。

null，当有部分或全
部失败时，不为空，
返回失败号码的信
息

对应错误码中的英
文描述

出参示例
全部成功

{

"success": true,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"failList": null

}

全部或部分失败

{

"success": false,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"failList": [

{

"mobile": "***********",

"errorCode": "35",

"errorDesc": "FAIL",

"uuid":"1442301"

},

{

"mobile": "***********",

"errorCode": "05",

"errorDesc": "FAIL",

"uuid":"1442303"

}

]

}

页 5


3. 下行（相同内容）

3.1.  地址

http 地址：http://域名:8080/sms/v2/send-same
https 地址：https://域名:8443/sms/v2/send-same
(   使用域名由客服经理提供)

3.2. 入参内容


一

二级

级

{}

list[]

字段名
账户

事务 ID

密码

内容

短信列表
手机号
自定义 id

扩展码

扩展参数

接口字段

account
transactionId
password

content
list
mobile
uuid

ext

extraParam

类型

String
String
String

String
list
String
String

String

String

长度

50

36

200

1000

14

32

16

1000

取值描述

由服务方提供的
账户

事务 ID

计算后的授权密
码

短信内容

下行短信列表
下行手机号

短信唯一标识，调
用方自己定义，用
于对应状态报告
值。

本条短信扩展码

本条短信的扩展
参数

备注

*必填：账号

*必填：幂等处理使用，要
求每次提交的值不相同

*必填：
account+pwd+transactionI
d          按照顺序排列（不包含
“+”字符）后，再经过标
准MD5 小写加密(其中
pwd 为客户密码，服务方
提供)

*必填：

上限 200 单次

没有扩展码时为null，合法
扩展为数字，否则都按照
无扩展处理

非填写

入参示例

{

"account": "ceshizhanghu",

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

页 6


"password":"5228537688dcbf85c87e59c1ae6b11a3",
"content":"短信下发内容",

"list": [

{

"mobile":"***********",
"uuid":"1442301",

"ext":"105"

},

{

"mobile":"***********",
"uuid":"1442302",

"ext":"01"

},

{

"mobile":"***********",
"uuid":"1442303",

"ext":null

}

]

}

3.3. 出参内容


一

二级

级

{}

List[]

字段名
成功标志

事务 ID

失败信息
列表

失败号码
错误码

错误信息
自定义 id

接口字段

success

transactionId

failList

mobile
errorCode

errorDesc
uuid

类型

boolean

String
list

String
String

String
String

长度   取值描述

返回true 表示都
下行成功，返回
false 表示部分或
全部失败

接口提交时的事
务ID 的值

返回失败部分号
码的信息

手机号

10   错误码，由服务
方定义

50   错误信息，由服
务方定义

32   短信唯一标识，
调 用 方 自 己 定

备注

全 部 成 功 时 为 空
null，当有部分或全
部失败时，不为空，
返回失败号码的信
息

对应错误码中的英
文描述

页 7


出参示例
全部成功

{

义，用于对应状
态报告值。

"success": true,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"failList": null

}

全部或部分失败

{

"success": false,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"failList": [

{

"mobile": "***********",

"errorCode": "35",

"errorDesc": "FAIL",

"uuid":"1442301"

},

{

"mobile": "***********",

"errorCode": "05",

"errorDesc": "FAIL",

"uuid":"1442303"

}

]

}

4. 状态报告

4.1. 客户获取

4.1.1. 地址

http 地址： http://域名:8080/sms/v2/pull-report
https 地址：https://域名:8443/sms/v2/pull-report
(   使用域名由客服经理提供)

页 8


4.1.2. 入参内容


一

二级  字段名

级

账户
事务 ID
密码

{}

拉取条数

接口字段

account
transactionId
password

count

类型

String
String
String

int

长度

50

36

200

取值描述

由服务方提供的
账户

事务 ID

由服务方提供密
码

一次拉取多少条
状态报告

备注

*必填：账号

*必填：幂等处理使用，要
求每次提交的值不相同

*必填：
account+pwd+transactionI
d          按照顺序排列（不包含
“+”字符）后，再经过标
准MD5 小写加密(其中
pwd 为客户密码，服务方
提供)

最大支持 200，填写非法
时按照默认值处理

入参示例

{

"account": "ceshizhanghu",

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"password":"5228537688dcbf85c87e59c1ae6b11a3",
"count": 200

}

4.1.3. 出参内容


一

二级  字段名

级

成功标志

事务 ID

{}      错误码

错误信息

状态报告
列表

接口字段

success

transactionId

errorCode
errorDesc
list

类型

boolean

String

String
String
list

长度  取值描述

接口调用成
功和失败

接 口 提 交
时，事务ID
的值

10   接口调用失
败错误码

50 接口调用失
败错误信息
返回状态报
告记录

备注

返回 true 表示获取成
功，返回 false 表示获取
失败

接口调用失败错误码

接口调用失败错误信
息

接口调用成功且没有
内容时为null,接口调用

页 9


List[]

业务账户
手机号

状态码

状态码对
应的错误
报告

报告到达
时间

自定义 id

扩展参数

userId
mobile

errorCode

errorDesc

time

uuid
extraParam

String
String

String

String

String

String
String

10

50

32

1000

业务账号
状态报告对
应手机号
状态报告状
态码： "0"
表示成功

状态报告错
误信息

原始短信到
达时间，格
式

yyyy-MM-d
d HH:mm:ss
与下行短信
做对应

本条短信的

扩展参数

失败时为null

服务方侧的错误码,(三
大服务方的状态码)

错误码代表的含义

非填写

出参示例
成功

{

"success": true,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"errorCode": "0",
"errorDesc":null,
"list": [

{

"userId": "yonghuzhanghao",
"mobile": "***********",

"time": "2016-05-29 10:55:23",

"errorCode": "0",
"errorDesc": "DELIVED",
"uuid": "1442301"

},

{

"userId": "yonghuzhanghao",
"mobile": "***********",

"time": "2016-05-29 10:55:25",

"errorCode": "0",
"errorDesc": "DELIVED",
"uuid": "1442302"

},

页 10


{

"userId": "yonghuzhanghao",
"mobile": "***********",

"time": "2016-05-29 11:55:23",

"errorCode": "20",
"errorDesc": "UNDELIVED",
"uuid": "1442303"

}

]

}

失败：

{

"success": false,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"errorCode": "50",
"errorDesc":"调用失败原因",
"list":  null

}

注：对于长短信，状态报告只可获取一条，以拆分时第一条状态报告结果为准。

4.2. 平台推送

4.2.1. 概述

本接口文档为客户 json 格式的状态报告及上行信息推送服务。信息推送时支持客户提
供的 http 和 https 两种协议类型的接收地址。

4.2.2. 状态报告格式

（1）一级参数说明


字段
receiver

pswd

数据类型
String

String

描述
账号

经过 md5 加密后的密码

是否必填
非必填

非必填

备注

为了合法身份的验
证，该值有双方约
定

为了合法身份的验
证，该值有双方约
定

页 11


transactionId

reports

String

JsonArray

事务 ID

状态报告内容

每次推送时该值都

必填    不同，可以该值作
为幂等处理，防止
数据重复接收

默认单次推送 10

必填 条，最大单次推送
保护超过 1000 条

（2）二级参数说明

字段 数据类型    描述 是否必填   备注


userId

String

短信下行时的账号    必填


mobile

String

手机号码         必填


errorCode

String

错误码，0 表示成功；其

他错误码均表示下发失 必填
败


errorDesc

String

错误码描述        必填


time

String

状态报告返回时间    必填


uuid

String

短信下发时的唯一值   必填


subSeq

String

长短信的拆分标识

非必填

同个业务账户该值
统一处理，若状态
报告中不需要体现
出长度短信的拆
分，则该值不推送；
若长需要体现出长
短短信，普通短信
该值为 0；长短信
时，该值对应长短
信拆分的值，如：1
2 3 。默认：长短
 信按照拆分方式推
送，即长短信推送
多条状态。

页 12


extraParam

String

本条信息的扩展参数

非必填

格式为 json 串，为
了以后程序扩展需
要

4.2.3. 入参示例

（1）推送方式：

Content-Type: application/json;charset=utf-8

（2）参数示例：

{

"pswd": "d542aae9e8e58daa515fa4c5f54c0be8",
"receiver": "pushUser",

"transactionId": "d542eauioe8e58daa515fa4c5f54c0be8",
"reports": [

{

"errorCode": "0",
"errorDesc": "DELIVERD",
"mobile": "15210980000",

"time": "2018-07-08 22:39:27",

"userId": "test",

"uuid": "3311c535-28a5-4523-8526-7c90bd731425"

},

{

"errorCode": "0",
"errorDesc": "DELIVERD",
"mobile": "15210980001",

"extraParam": "{\"department\":\"运维部\"}",

"time": "2018-07-08 22:39:27",

"userId": "test",

"uuid": "58b40e89-e593-4f9f-897e-45cc15f96432"

},

{

"errorCode": "0",
"errorDesc": "DELIVERD",
"mobile": "15210980002",

"time": "2018-07-08 22:39:27",

"userId": "test",

"uuid": "66c34284-eb58-4faa-a12b-3b39d3cebac7"

}

]

}

页 13


4.2.4. 响应

接收状态报告端返回 ok 两个字母（不区分大小写），为正常接收到了推送的状态报告；
若返回其他值任意值，则为接收推送的状态报告失败。我方对于接收失败的会进行三次的重
新推送。

5. 上行

5.1. 客户获取

5.1.1. 地址

http 地址： http://域名:8080/sms/v2/pull-deliver
https 地址：https://域名:8443/sms/v2/pull-deliver

(使用域名由客服经理提供)

5.1.2. 入参内容


一

二级 字段名

级

账户
事务ID
密码

{}

拉取条
数

接口字段

account
transactionId
password

count

类型

String
String
String

int

长度

50

36

200

取值描述

由服务方提供的账
户

事务 ID

计算后的授权密码

一次拉取多少条上
行记录

备注

*必填：账号

*必填：幂等处理使用，要
求每次提交的值不相同

*必填：
account+pwd+transactionI
d          按照顺序排列（不包含
“+”字符）后，再经过标
准MD5 小写加密(其中
pwd 为客户密码，服务方

提供)

最大支持 200，填写非法
时按照默认值处理

入参示例

{

"account": "ceshizhanghu",

页 14


"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"password":"5228537688dcbf85c87e59c1ae6b11a3",
"count": 200

}

5.1.3. 出参内容


一

二级

级

{}

List[]

字段名
成功标志

事务 ID
错误码
错误信息

上行短信
列表

手机号
业务账户
内容

上行时间

扩展码
接入号
扩展参数

接口字段

success

transactionId
errorCode
errorDesc
list

mobile
userId
content
time

ext
spNumber
extraParam

类型

boolean

String
String
String
list

String
String
String
String

String
String
String

长度   取值描述

接口调用成功
和失败

接口提交时，
事务ID 的值

10  接口调用失败
错误码

50 接口调用失败
错误信息

返回上行记录

上行手机号
业务账户

70   上行短信内容
用 户 上 行 时
间 ， 格 式

yyyy-MM-dd
HH:mm:ss

上行短信扩展

码

手机终端回复
的号码

本条短信的扩
展参数

备注

返回 true 表示获取成
功，返回 false 表示获取
失败

接口调用失败错误码

接口调用失败错误信
息

没有可获取内容时为
空，失败时为空 null

没有扩展码时为 null

非填写

出参示例
成功

{

"success": true,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"errorCode": "0",

"errorDesc":"",
"list": [

页 15


{

"userId":"ceshizhanghu",
"mobile": "***********",

"content": "短信上行内容 1",

"time":"2016-05-29 10:55:23",

"spNumber":"10690032",
"ext": "105"

},

{

"userId":"ceshizhanghu",
"mobile": "***********",

"content": "短信上行内容 2",

"time":"2016-05-29 10:55:24",

"spNumber":"10690032",
"ext": "01"

},

{

"userId":"ceshizhanghu",
"mobile": "***********",

"content": "短信上行内容 3",

"time":"2016-05-29 12:55:23",

"spNumber":"10690032",
"ext": null

}

]

}

失败：

{

"success": false,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"errorCode": "50",
"errorDesc":"调用失败原因",
"list":  null

}

5.2. 平台推送

5.2.1. 概述

本接口文档为客户 json 格式的状态报告及上行信息推送服务。信息推送时支持客户提
供的 http 和 https 两种协议类型的接收地址。

页 16


5.2.2. 上行信息格式

（1）一级参数说明


字段
receiver

数据类型  描述

String   账号

是否必填
非必填

备注

为了合法身份的验
证，该值有双方约
定


pswd

transactionId

String

String

经过 md5 加密后的密码

事务 ID

非必填

必填

为了合法身份的验
证，该值有双方约
定

每次推送时该值都

不同，可以该值作
为幂等处理，防止
数据重复接收


delivers

JsonArray

上行信息内容       必填

（2）二级参数说明

字段 数据类型    描述 是否必填   备注


userId

String

短信下行时的账号    必填


mobile

String

手机号码         必填


content

String

手机端上行的短信内容  必填


ext

String

下行时的自定义扩展号  必填

页 17


time

String

手机端上行信息的时间  必填


extraParam

String

本条信息的扩展参数

非必填

格式为 json 串，为
了以后程序扩展需
要

5.2.3. 入参示例

（1）推送方式：

Content-Type: application/json;charset=utf-8

（2）参数示例：

{

"receiver": "pushUser",

"pswd": "d542aae9e8e58daa515fa4c5f54c0be8",
"transactionId": "d542eauioe8e58daa515fa4c5f54c0be8",
"delivers":  [

{

"content": "TD",

"ext": "0",

"mobile": "15210980000",

"time": "2018-07-08 22:57:57",

"userId": "test"

},

{

"content": "TD",

"ext": "1",

"mobile": "15210980001",

"time": "2018-07-08 22:57:57",

"userId": "test"

}

]

}

5.2.4. 响应

接收上行信息端返回 ok 两个字母（不区分大小写），为正常接收到了推送的上行信息；
若返回其他值任意值，则为接收推送的上行信息失败。我方对于接收失败的会进行三次的重
新推送。

页 18


6. 余额查询

查询预付费客户的当前余额信息，对于后付费账号可忽略该接口。

6.1.  地址

http 地址： http://域名:8080/sms/v2/user-balance
https 地址：https://域名:8443/sms/v2/user-balance
(   使用域名由客服经理提供)

6.1.1. 入参内容


一

二级  字段名

级

账户
事务 ID

密码

{}

接口字段

account
transactionId
password

类型

String
String
String

长度

50

36

200

取值描述

由服务方提供
的账户

事务 ID

由服务方提供
密码

备注

*必填：账号

*必填：幂等处理使用，要
求每次提交的值不相同

*必填：
account+pwd+transactionId
          按照顺序排列（不包含“+”
字符）后，再经过标准 MD5
小写加密(其中pwd 为客户
密码，服务方提供)

入参示例

{

"account": "ceshizhanghu",

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"password":"5228537688dcbf85c87e59c1ae6b11a3"

}

6.1.2. 出参内容


一

二级 字段名

级

成功标志

{}

事务 ID

接口字段

success

transactionId

类型

boolean

String

长度 取值描述

接口调用成
功和失败

接 口 提 交

备注

返回 true 表示获取成
功，返回 false 表示获取
失败

页 19


账户余额
错误码

错误信息

balance

errorCode

errorDesc

int
String

String

10

50

100

时，事务ID

的值

账户余额
接口调用失
败错误码

接口调用失
败错误信息

接口调用失败错误码，
1000 为成功，其他值均
为失败

接口调用失败错误信
息

出参示例
成功

{

"success": true,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",
"balance":1230,

"errorCode": "0",

"errorDesc":"成功"

}

失败：

{

"success": false,

"transactionId":"8298-5374258-dcbf85-c87e5-9c1ae6-bfg73",

"errorCode": "50",
"errorDesc":"调用失败原因"

}

7. 错误码说明


返回代码
SMS00000
SMS00001
SMS00002
SMS00003
SMS00004
SMS00005
SMS00006
SMS00007

英文描述
SUCCESS
USER_ID_ERROR
PWD_ERROR
ILLEGAL_IP_ERROR
SERVICE_ERROR

NO_MONEY_ERROR
ILLEGAL_MOBILE_ERROR
TOO_LESS_MOBILES_ERROR

代码说明
提交成功

账户不存在或者已经关闭

密码错误
 非法 IP 访问
业务不存在

用户余额不足
非法手机号码

不存在可用的手机号码

页 20


SMS00008
SMS00009

SMS00010
SMS00011
SMS00012
SMS00013
SMS00014
SMS00015
SMS00016
SMS00017
SMS00018
SMS00019

TOO_MOUCH_MOBILES_ERROR
TOO_MOUCH_CONTENT

OTHER_ERROR

TOO HIGH FREQUENCY
NO PERMISSIONS
MOBILE_SERVICE_ERROR
ILLEGAL_JSON_DATA
MOBILE_IS_NULL
CONTENT_IS_NULL
LIST_IS_NULL

TRANSATIONID_IS_ERROR

TYPE_ERROR

单次提交的手机号码超过 200 个
提交的短信内容长度非法,总长度
不可以超过 1000 字)

未知错误

访问频率过高(不低于 200ms)

没有使用该接口的权限
手机号码和业务不符
非 json 格式数据

手机号码参数[mobile]未填写
短信内容参数[content]未填写
参数[list]未填写

参数[transactionId]填写非法
非预付费客户

页 21

